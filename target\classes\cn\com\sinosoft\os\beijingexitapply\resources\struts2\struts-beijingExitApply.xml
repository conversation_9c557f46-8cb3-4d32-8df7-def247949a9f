<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE struts PUBLIC
	"-//Apache Software Foundation//DTD Struts Configuration 2.0//EN"
	"http://struts.apache.org/dtds/struts-2.0.dtd">
<struts>
	<package name="beijingExitApply" extends="bsp-default" namespace="/cn/com/sinosoft/os/beijingexitapply">
		<!-- 查询 -->
		<action name="qryBeijingExitApply" class="beijingExitApplyAction" method="qryParentInput">
			<param name="sessionGroup">beijingExitApply</param>
			<result name="success">
				/WEB-INF/pages/os/cn/com/sinosoft/os/beijingexitapply/qryBeijingExitApply.jsp
			</result>
		</action>
		<action name="qryBeijingExitApplyList" class="commonQueryAction">
			<param name="sessionGroup">beijingExitApply</param>
			<param name="queryCode">QRY_OS_BEIJING_EXIT_APPLY</param>
			<param name="resultName">qryList</param>
		</action>
		<!-- 查看 -->
		<action name="beijingExitApply_viewParent" class="beijingExitApplyAction" method="viewParent">
			<param name="sessionGroup">beijingExitApply</param>
			<result name="success">
				/WEB-INF/pages/os/cn/com/sinosoft/os/beijingexitapply/edtBeijingExitApply.jsp
			</result>
		</action>
		<!-- 添加 -->
		<action name="beijingExitApply_addParentInput" class="beijingExitApplyAction" method="addParentInput">
			<param name="sessionGroup">beijingExitApply</param>
			<result name="success">
				/WEB-INF/pages/os/cn/com/sinosoft/os/beijingexitapply/edtBeijingExitApply.jsp
			</result>
		</action>
		<action name="beijingExitApply_addParentSubmit" class="beijingExitApplyAction" method="addParentSubmit">
			<param name="sessionGroup">beijingExitApply</param>
		</action>
		<!-- 修改 -->
		<action name="beijingExitApply_edtParentInput" class="beijingExitApplyAction" method="edtParentInput">
			<param name="sessionGroup">beijingExitApply</param>
			<result name="success">
				/WEB-INF/pages/os/cn/com/sinosoft/os/beijingexitapply/edtBeijingExitApply.jsp
			</result>
		</action>
		<action name="beijingExitApply_edtParentSubmit" class="beijingExitApplyAction" method="edtParentSubmit">
			<param name="sessionGroup">beijingExitApply</param>
		</action>
		<!-- 删除 -->
		<action name="beijingExitApply_delParentSubmit" class="beijingExitApplyAction" method="delParentSubmit">
			<param name="sessionGroup">beijingExitApply</param>
		</action>
		
		<!-- 打开审批页面 -->
		<action name="beijingExitApply_auditParentInput" class="beijingExitApplyAction" method="auditParentInput">
			<param name="sessionGroup">beijingExitApply</param>
			<result name="success">
			     /WEB-INF/pages/os/cn/com/sinosoft/os/beijingexitapply/auditBeijingExitApply.jsp
			</result>
		</action>
		<!-- 打开订正页面 -->
		<action name="beijingExitApply_auditEditParentInput" class="beijingExitApplyAction" method="auditEditParentInput">
			<param name="sessionGroup">beijingExitApply</param>
			<result name="success">
			    /WEB-INF/pages/os/cn/com/sinosoft/os/beijingexitapply/edtBeijingExitApply.jsp
			</result>
		</action>
		
		<!-- 审批 applySubmit-->
		<action name="auditBeijingExitApplySubmit" class="beijingExitApplyAction" method="applySubmit">
			<param name="sessionGroup">beijingExitApply</param>
		</action>
		
		<!-- 附件在线编辑-->
		<action name="beijingExitApply_OnlineEdit" class="beijingExitApplyAction" method="onlineEditInput">
			<param name="sessionGroup">beijingExitApply</param>
			<result name="success">
				/WEB-INF/pages/os/cn/com/sinosoft/os/beijingexitapply/viewForWebOffice.jsp
			</result>
		</action>
		
		<!-- 在线编辑保存提交-->
		<action name="beijingExitApply_onlineEditSubmit" class="beijingExitApplyAction" method="onlineEditSubmit">
			<param name="sessionGroup">beijingExitApply</param>
		</action>
	</package>
</struts>

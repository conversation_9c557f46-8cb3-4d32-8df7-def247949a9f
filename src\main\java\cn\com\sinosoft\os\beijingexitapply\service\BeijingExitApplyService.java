package cn.com.sinosoft.os.beijingexitapply.service;

import cn.com.sinosoft.os.beijingexitapply.model.BeijingExitApply;

/**
 * �������� - service�ӿ�.
 *
 * <AUTHOR>
 * @date: 2025/08/02 15:41:43
 * @version V1.0
 */
public interface BeijingExitApplyService {

	/**
	 * ����id��ȡ����������Ϣ
	 *
	 * @param id
	 * @return
	 */
	public BeijingExitApply get(String id);

	/**
	 * ɾ������������Ϣ
	 *
	 * @param ids
	 */
	public void delete(String ids);

	/**
	 * �������������Ϣ
	 *
	 * @param result
	 */
	public void save(BeijingExitApply result);

	/**
	 * �޸ĳ���������Ϣ
	 *
	 * @param result
	 */
	public void edit(BeijingExitApply result);

	/**
	 * ��������ʵ��id��ȡ����������Ϣ
	 *
	 * @param piId
	 * @return
	 */
	public BeijingExitApply getPiId(String piId);

	/**
	 * �ύ����
	 *
	 * @param result
	 * @param parama
	 * @param taskid
	 * @param piId
	 */
	public void applySubmit(BeijingExitApply result, String parama, String taskid, String piId);

	/**
	 * ��������
	 *
	 * @param id
	 */
	public void revokeApply(String id);

}
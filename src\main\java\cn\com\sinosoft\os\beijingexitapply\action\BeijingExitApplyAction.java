package cn.com.sinosoft.os.beijingexitapply.action;

import cn.com.sinosoft.os.beijingexitapply.service.BeijingExitApplyService;
import cn.com.sinosoft.os.beijingexitapply.model.BeijingExitApply;
import ie.bsp.frame.action.BaseEditAction;
import ie.bsp.frame.exception.GeneralException;

/**
 * �������� - Action.
 *
 * <AUTHOR>
 * @date: 2025/08/02 15:41:43
 * @version V1.0
 */
public class BeijingExitApplyAction extends BaseEditAction {

	/**
	 * Ĭ�Ϲ��캯��.
	 */
	public BeijingExitApplyAction() {
		moduleId = "OS9000000";
	}

	private static final long serialVersionUID = 1L;

	// ��������Service
	private BeijingExitApplyService service;

	// ��������ʵ��
	private BeijingExitApply result;

	// ����ID
	private String id;

	// ����ʵ��ID
	private String piId;

	// ����ID
	private String taskid;

	// ��������
	private String parama;

	/**
	 * ��ȡ ��������Service.
	 *
	 * @return ��������Service
	 */
	public BeijingExitApplyService getService() {
		return service;
	}

	/**
	 * ���� ��������Service.
	 *
	 * @param service ��������Service
	 */
	public void setService(BeijingExitApplyService service) {
		this.service = service;
	}

	/**
	 * ��ȡ ��������ʵ��.
	 *
	 * @return ��������ʵ��
	 */
	public BeijingExitApply getResult() {
		return result;
	}

	/**
	 * ���� ��������ʵ��.
	 *
	 * @param result ��������ʵ��
	 */
	public void setResult(BeijingExitApply result) {
		this.result = result;
	}

	/**
	 * ��ȡ ����ID.
	 *
	 * @return ����ID
	 */
	public String getId() {
		return id;
	}

	/**
	 * ���� ����ID.
	 *
	 * @param id ����ID
	 */
	public void setId(String id) {
		this.id = id;
	}

	/**
	 * ��ȡ ����ʵ��ID.
	 *
	 * @return ����ʵ��ID
	 */
	public String getPiId() {
		return piId;
	}

	/**
	 * ���� ����ʵ��ID.
	 *
	 * @param piId ����ʵ��ID
	 */
	public void setPiId(String piId) {
		this.piId = piId;
	}

	/**
	 * ��ȡ ����ID.
	 *
	 * @return ����ID
	 */
	public String getTaskid() {
		return taskid;
	}

	/**
	 * ���� ����ID.
	 *
	 * @param taskid ����ID
	 */
	public void setTaskid(String taskid) {
		this.taskid = taskid;
	}

	/**
	 * ��ȡ ��������.
	 *
	 * @return ��������
	 */
	public String getParama() {
		return parama;
	}

	/**
	 * ���� ��������.
	 *
	 * @param parama ��������
	 */
	public void setParama(String parama) {
		this.parama = parama;
	}

	@Override
	public void doQryParentInput() throws GeneralException {
		// ��ѯҳ���ʼ��
	}

	@Override
	public void doViewParentSubmit() throws GeneralException {
		funcId = "OS9000001";
		result = service.get(id);
		setPiId(result.getPiId());
	}

	@Override
	public void doDelteParentSubmit() throws GeneralException {
		funcId = "OS9000005";
		service.delete(id);
	}

	@Override
	public void doAddParentInput() throws GeneralException {

	}

	@Override
	public void doAddParentSubmit() throws GeneralException {
		funcId = "OS9000002";
		service.save(result);
	}

	@Override
	public void doEditParentInput() throws GeneralException {
		doViewParentSubmit();
	}

	@Override
	public void doEditParentSubmit() throws GeneralException {
		funcId = "OS9000003";
		service.edit(result);
	}

	@Override
	public void doAudit() throws GeneralException {
		funcId = "";
		service.applySubmit(result, parama, taskid, piId);
		writeToPage("<script>parent.CloseWindow();</script>");
	}

	/**
	 * ����ҳ������
	 *
	 * @return String
	 */
	public String auditParentInput() {
		// ͨ��piid��ȡʵ��
		result = service.getPiId(piId);
		return SUCCESS;
	}

	/**
	 * ��������
	 */
	public void revokeApply() throws GeneralException {
		service.revokeApply(id);
		writeToPage("<script>parent.CloseWindow('revoke');</script>");
	}

}
